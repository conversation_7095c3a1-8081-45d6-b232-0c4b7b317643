import { StrictMode } from 'react'
import { Suspense } from 'react'
import { createRoot } from 'react-dom/client'
import { Provider } from 'react-redux'
import { BrowserRouter } from 'react-router-dom'

import { Operation as UserOperation } from '@/reducer/user/user'
import configurateStore from '@/store/store.js'
import { initYupMessages } from '@/utils/initYupMessages'

import App from './App.jsx'

import './css/index.scss'

const store = configurateStore()

store.dispatch(UserOperation.checkAuth())

initYupMessages()

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <BrowserRouter>
      <Suspense fallback="loading">
        <Provider store={store}>
          <App />
        </Provider>
      </Suspense>
    </BrowserRouter>
  </StrictMode>
)
