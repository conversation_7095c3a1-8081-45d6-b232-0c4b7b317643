.teamsTabs {
  margin-top: 20px;
}

.tabList {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  border-bottom: 2px solid #e0e0e0;
  margin-bottom: 20px;
}

.tab {
  padding: 12px 20px;
  cursor: pointer;
  border: none;
  background: none;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  
  &:hover {
    color: #007bff;
    background-color: #f8f9fa;
  }
  
  &[aria-selected="true"] {
    color: #007bff;
    border-bottom-color: #007bff;
    background-color: #f8f9fa;
  }
}

.tabPanel {
  outline: none;
}

.teamsList {
  margin-bottom: 20px;
}

.title {
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.teamsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
  max-height: 400px;
  overflow-y: auto;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #fafafa;
}

.teamItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
  transition: all 0.2s ease;
  cursor: pointer;
  
  &:hover {
    background-color: #f0f8ff;
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}

.teamNumber {
  font-size: 18px;
  font-weight: 700;
  color: #007bff;
  margin-bottom: 5px;
}

.teamId {
  font-size: 12px;
  color: #666;
  font-family: monospace;
}

.message {
  padding: 30px;
  text-align: center;
  color: #666;
  font-style: italic;
  background-color: #f9f9f9;
  border-radius: 6px;
  margin-bottom: 20px;
}

.error {
  padding: 20px;
  text-align: center;
  color: #d32f2f;
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 6px;
  margin-bottom: 20px;
}
