import { Tab, Tab<PERSON>ist, Tab<PERSON>anel, Tabs } from 'react-tabs'

import { useGetFreeTeamsWithoutTickets } from '@/features/teams/api/getFreeTeamsWithoutTickets'
import { useGetUsedNumbers } from '@/features/teams/api/getUsedNumbers'

import styles from './TeamsTabs.module.scss'

const TeamsList = ({ teams, title, isLoading, error }) => {
  if (isLoading) {
    return <p className={styles.message}>Загрузка...</p>
  }

  if (error) {
    return <p className={styles.error}>Ошибка загрузки: {error.message}</p>
  }

  if (!teams || teams.length === 0) {
    return <p className={styles.message}>Нет команд</p>
  }

  return (
    <div className={styles.teamsList}>
      <div className={styles.commonStat}>
        <p>
          {title}: {teams.length}
        </p>
      </div>
      <div className={styles.teamsGrid}>
        {teams.map((team, index) => (
          <div key={team.public_id || index} className={styles.teamItem}>
            {team.number}
          </div>
        ))}
      </div>
    </div>
  )
}

const TeamsTabs = ({ eventCityId }) => {
  const { data: usedNumbersResponse, isLoading: isLoadingUsed, error: errorUsed } = useGetUsedNumbers(eventCityId)

  const {
    data: freeTeamsResponse,
    isLoading: isLoadingFree,
    error: errorFree,
  } = useGetFreeTeamsWithoutTickets(eventCityId)

  const usedTeams = usedNumbersResponse?.data?.values || []
  const freeTeams = freeTeamsResponse?.data?.values || []

  if (!eventCityId) {
    return <p className={styles.message}>Выберите событие для отображения команд</p>
  }

  return (
    <div className={styles.teamsTabs}>
      <Tabs>
        <TabList className={styles.tabList}>
          <Tab className={styles.tab}>Все свободные команды</Tab>
          <Tab className={styles.tab}>Все занятые команды</Tab>
        </TabList>

        <TabPanel className={styles.tabPanel}>
          <TeamsList
            teams={freeTeams}
            title="Свободные команды (без билетов)"
            isLoading={isLoadingFree}
            error={errorFree}
          />
        </TabPanel>

        <TabPanel className={styles.tabPanel}>
          <TeamsList teams={usedTeams} title="Занятые команды" isLoading={isLoadingUsed} error={errorUsed} />
        </TabPanel>
      </Tabs>
    </div>
  )
}

export default TeamsTabs
