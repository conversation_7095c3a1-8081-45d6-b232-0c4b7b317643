import { useState } from 'react'

import FormatSelector from './FormatSelector'
import NumbersList from './NumbersList'
import styles from './TeamsTab.module.scss'
import TeamsTabs from './TeamsTabs'

const TeamsTab = ({ city }) => {
  const [selectedFormatId, setSelectedFormatId] = useState('')

  const handleFormatChange = (formatId) => {
    setSelectedFormatId(formatId)
  }

  return (
    <div className={styles.teamsTab}>
      <div className={styles.section}>
        <FormatSelector
          selectedFormatId={selectedFormatId}
          onFormatChange={handleFormatChange}
          eventCityId={city?.eventCityId}
        />
      </div>

      {selectedFormatId && (
        <div className={styles.section}>
          <NumbersList formatId={selectedFormatId} />
        </div>
      )}

      <div className={styles.section}>
        <TeamsTabs eventCityId={city?.eventCityId} />
      </div>
    </div>
  )
}

export default TeamsTab
