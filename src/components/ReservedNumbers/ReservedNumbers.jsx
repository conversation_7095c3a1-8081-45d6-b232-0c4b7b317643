import { useEffect, useState, useRef } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Tab, <PERSON>b<PERSON>ist, TabPanel, Tabs } from 'react-tabs'

import UniversalPopup from '@/components/Popups/UniversalPopup/UniversalPopup'

import { ActionCreator as ActionCreatorMemReg, ActionCreator } from '@/reducer/memberRegistration/registration'
import { Operation as RegistrationOperation } from '@/reducer/memberRegistration/registration'
import {
  getDelReservedNumber,
  getEventFormats,
  getFreeFormatNumbers,
  getReservedNumbers,
  getReservedNumberStatus,
} from '@/reducer/memberRegistration/selectors'
import { getThemeMemReg } from '@/reducer/theme/selectors'

import styles from './ReservedNumbers.module.scss'

const returnFormatName = (formats, formatId) => {
  const format = formats.find((item) => item.public_id === formatId)

  if (format) return format.title

  return formatId
}

const ReservedNumbers = ({ city }) => {
  const theme = useSelector((state) => getThemeMemReg(state))
  const reservedNumbers = useSelector((state) => getReservedNumbers(state))
  const freeFormatNumbers = useSelector((state) => getFreeFormatNumbers(state))
  const isDelReservedNumber = useSelector((state) => getDelReservedNumber(state))
  const isReservedNumber = useSelector((state) => getReservedNumberStatus(state))
  const eventFormats = useSelector((state) => getEventFormats(state))
  const [filteredFormatLabels, setFilteredFormatLabels] = useState({})
  const [selectedNumberId, setSelectedNumberId] = useState('')
  const [selectedFreeNumber, setSelectedFreeNumber] = useState({})
  const [columnLength, setColumnLength] = useState(0)

  const dispatch = useDispatch()
  const prevCityIdRef = useRef(null)

  useEffect(() => {
    if (city?.eventCityId && prevCityIdRef.current !== city.eventCityId) {
      dispatch(RegistrationOperation.loadEventFormats(city.eventCityId))
      dispatch(RegistrationOperation.loadFreeFormatNumbers(city.eventCityId))

      prevCityIdRef.current = city.eventCityId
    }
  }, [city.eventCityId, dispatch])

  useEffect(() => {
    if (reservedNumbers?.length > 0) {
      let uniqueFormats = {}
      reservedNumbers.forEach((item) => {
        uniqueFormats[item.event_format.public_id] = []
      })

      reservedNumbers.forEach((item) => uniqueFormats[item.event_format.public_id].push(item))

      let sorted = Object.values(uniqueFormats).sort((prev, next) => {
        if (prev.length > next.length) return -1
        if (prev.length < next.length) return 1
      })

      setFilteredFormatLabels(uniqueFormats)
      setColumnLength(sorted[0]?.length)
    }
  }, [reservedNumbers])

  const handleClickNumber = (number) => {
    setSelectedNumberId(number.public_id)
  }

  const handleClickDeleteNumber = () => {
    dispatch(RegistrationOperation.deleteReservedNumber(selectedNumberId, city.eventCityId))
    setSelectedNumberId('')
  }

  const handleClickFreeNumber = (format, number) => {
    setSelectedFreeNumber({
      event_format: {
        public_id: format.event_format.public_id,
      },
      number,
      event_city: {
        public_id: city.eventCityId,
      },
    })
  }

  const handleClickReserveNumber = () => {
    dispatch(RegistrationOperation.reservedNumber(selectedFreeNumber))
    setSelectedFreeNumber({})
  }

  const fillColumn = (values, length = columnLength) => {
    let cells = []

    for (let i = 0; i < length; i++) {
      if (values[i]) {
        cells.push(values[i])
      } else {
        cells.push(null)
      }
    }

    return cells
  }

  return (
    <div className={theme ? `${styles.mainWhite}` : ``}>
      <div className={styles.tabsWrap}>
        <Tabs>
          <TabList>
            <Tab>Зарезервированные номера</Tab>
            <Tab>Свободные номера</Tab>
            <Tab>Команды</Tab>
          </TabList>
          <TabPanel>
            {reservedNumbers?.length > 0 ? (
              <>
                <div className={styles.commonStat}>
                  <p>Всего зарезервированных номеров: {reservedNumbers?.length}</p>
                </div>

                <div className={styles.table}>
                  <div className={styles.thWrap}>
                    {Object.keys(filteredFormatLabels).map((item) => (
                      <div className={styles.th} key={item}>
                        {returnFormatName(eventFormats, item)}
                      </div>
                    ))}
                  </div>

                  <div className={styles.tableColumnWrap}>
                    {Object.entries(filteredFormatLabels).map(([key, values]) => (
                      <div className={styles.tableColumn} key={key}>
                        {fillColumn(values).map((value, idx) =>
                          value ? (
                            <div
                              className={styles.tableCell}
                              onClick={() => handleClickNumber(value)}
                              key={value.public_id}
                            >
                              {value.number}
                            </div>
                          ) : (
                            <div
                              className={`${styles.tableCell} ${styles.tableCellEmpty}`}
                              key={`empty-reserved-${key}-${idx}`}
                            />
                          )
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </>
            ) : (
              <p>Нет зарезервированных номеров</p>
            )}
          </TabPanel>

          <TabPanel>
            {freeFormatNumbers?.length > 0 ? (
              <>
                <div className={styles.commonStat}>
                  {freeFormatNumbers.map((item) => (
                    <p key={item.event_format.public_id}>
                      {returnFormatName(eventFormats, item.event_format.public_id)}: {item.numbers?.length}
                    </p>
                  ))}
                </div>

                <div className={styles.tableWrap}>
                  <div className={styles.table}>
                    <div className={styles.thWrap}>
                      {freeFormatNumbers.map((item) => (
                        <div className={styles.th} key={item.event_format.public_id}>
                          {returnFormatName(eventFormats, item.event_format.public_id)}
                        </div>
                      ))}
                    </div>

                    <div className={styles.tableColumnWrap}>
                      {freeFormatNumbers?.map((item) => (
                        <div className={styles.tableColumn} key={item.event_format.public_id}>
                          {fillColumn(item.numbers, 100).map((value, idx) =>
                            value ? (
                              <div
                                className={styles.tableCell}
                                onClick={() => handleClickFreeNumber(item, value)}
                                key={`${item.event_format.public_id}-${value}`}
                              >
                                {value}
                              </div>
                            ) : (
                              <div
                                className={`${styles.tableCell} ${styles.tableCellEmpty}`}
                                key={`empty-free-${item.event_format.public_id}-${idx}`}
                              />
                            )
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <p>Нет свободных номеров</p>
            )}
          </TabPanel>

          <TabPanel>
            <p>Команды</p>
          </TabPanel>
        </Tabs>

        <button
          className={styles.btn}
          onClick={() => dispatch(ActionCreatorMemReg.setIsOpenReservedNumbers(false))}
          type="button"
        >
          Назад
        </button>
      </div>

      {selectedNumberId.length > 0 && (
        <UniversalPopup>
          <div className={styles.popupWrapper}>
            <h2 className={styles.popupTitle}>Удалить номер?</h2>
            <div className={styles.popupBtnWrap}>
              <button className={styles.popupBtn} onClick={() => setSelectedNumberId('')}>
                Отмена
              </button>
              <button className={styles.popupBtn} onClick={handleClickDeleteNumber}>
                Удалить
              </button>
            </div>
          </div>
        </UniversalPopup>
      )}

      {Object.keys(selectedFreeNumber)?.length > 0 && (
        <UniversalPopup>
          <div className={styles.popupWrapper}>
            <h2 className={styles.popupTitle}>Зарезервировать номер?</h2>
            <div className={styles.popupBtnWrap}>
              <button className={styles.popupBtn} onClick={() => setSelectedFreeNumber({})}>
                Отмена
              </button>
              <button className={styles.popupBtn} onClick={handleClickReserveNumber}>
                Да
              </button>
            </div>
          </div>
        </UniversalPopup>
      )}

      {isDelReservedNumber && (
        <UniversalPopup>
          <div className={styles.popupWrapper}>
            <h2 className={styles.popupTitle}>Номер удалён.</h2>
            <div className={styles.popupBtnWrap}>
              <button className={styles.popupBtn} onClick={() => dispatch(ActionCreator.setDelReservedNumber(false))}>
                Закрыть
              </button>
            </div>
          </div>
        </UniversalPopup>
      )}

      {isReservedNumber && (
        <UniversalPopup>
          <div className={styles.popupWrapper}>
            <h2 className={styles.popupTitle}>Номер зарезервирован.</h2>
            <div className={styles.popupBtnWrap}>
              <button
                className={styles.popupBtn}
                onClick={() => dispatch(ActionCreator.setReservedNumberStatus(false))}
              >
                Закрыть
              </button>
            </div>
          </div>
        </UniversalPopup>
      )}
    </div>
  )
}

export default ReservedNumbers
