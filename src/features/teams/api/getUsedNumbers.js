import { useQuery } from '@tanstack/react-query'

import { createAPI } from '@/api'
import { returnAuthConfig } from '@/utils/auth'

const api = createAPI()

// Функция для получения занятых номеров команд в событии
const getUsedNumbers = (eventCityPublicId) => {
  const config = returnAuthConfig()
  return api.get(`/api/volunteer/teams/event_city/${eventCityPublicId}/used/numbers`, config)
}

// Ключ запроса для React Query
export const GET_USED_NUMBERS_KEY = 'usedNumbers'

// Хук React Query для получения занятых номеров команд
export const useGetUsedNumbers = (eventCityPublicId) => {
  return useQuery({
    queryKey: [GET_USED_NUMBERS_KEY, eventCityPublicId],
    queryFn: () => getUsedNumbers(eventCityPublicId),
    staleTime: 5 * 60 * 1000, // 5 минут
    refetchOnWindowFocus: false,
    enabled: !!eventCityPublicId, // Запрос выполняется только если есть eventCityPublicId
  })
}
